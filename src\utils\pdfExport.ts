import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';

interface ExportToPDFOptions {
  element: HTMLElement;
  filename: string;
  title?: string;
  quality?: number;
}

export const exportToPDF = async ({ element, filename, title, quality = 2 }: ExportToPDFOptions): Promise<boolean> => {
  try {
    // 验证输入参数
    if (!element) {
      throw new Error('导出元素不能为空');
    }

    if (!filename) {
      throw new Error('文件名不能为空');
    }

    // 确保元素在视口中可见
    element.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // 等待滚动完成
    await new Promise((resolve) => setTimeout(resolve, 300));

    console.log('开始生成canvas，元素尺寸:', {
      width: element.offsetWidth,
      height: element.offsetHeight,
      scrollWidth: element.scrollWidth,
      scrollHeight: element.scrollHeight,
    });

    // 生成canvas，使用更好的配置
    const canvas = await html2canvas(element, {
      scale: quality,
      useCORS: true,
      allowTaint: false,
      logging: false,
      backgroundColor: '#ffffff',
      width: element.scrollWidth,
      height: element.scrollHeight,
      scrollX: 0,
      scrollY: 0,
      windowWidth: element.scrollWidth,
      windowHeight: element.scrollHeight,
      onclone: (clonedDoc) => {
        // 确保克隆的文档中的样式正确应用
        const clonedElement = clonedDoc.querySelector(`.${element.className.split(' ')[0]}`);
        if (clonedElement) {
          (clonedElement as HTMLElement).style.overflow = 'visible';
          (clonedElement as HTMLElement).style.height = 'auto';
          (clonedElement as HTMLElement).style.maxHeight = 'none';
        }
      },
    });

    if (!canvas || canvas.width === 0 || canvas.height === 0) {
      throw new Error('生成的canvas无效，请检查导出内容');
    }

    console.log('Canvas生成成功:', {
      width: canvas.width,
      height: canvas.height,
    });

    // 转换为图片数据
    const imgData = canvas.toDataURL('image/jpeg', 0.95);

    if (!imgData || imgData === 'data:,') {
      throw new Error('图片数据生成失败');
    }

    // 像素转毫米的转换函数
    const pxToMm = (px: number) => px * 0.264583;

    const imgWidth = pxToMm(canvas.width);
    const imgHeight = pxToMm(canvas.height);

    const margin = 10;
    const titleOffset = title ? 40 : 0;

    // 计算PDF尺寸，但限制最大尺寸避免过大的PDF
    const maxWidth = 210; // A4宽度
    const maxHeight = 297; // A4高度

    let pdfWidth = Math.min(imgWidth + margin * 2, maxWidth * 3);
    let pdfHeight = Math.min(imgHeight + margin * 2 + titleOffset, maxHeight * 5);

    // 如果内容太大，按比例缩放
    let scaleFactor = 1;
    if (pdfWidth > maxWidth * 3 || pdfHeight > maxHeight * 5) {
      const widthScale = (maxWidth * 3) / pdfWidth;
      const heightScale = (maxHeight * 5) / pdfHeight;
      scaleFactor = Math.min(widthScale, heightScale);
      pdfWidth *= scaleFactor;
      pdfHeight *= scaleFactor;
    }

    const pdf = new jsPDF({
      orientation: pdfWidth > pdfHeight ? 'landscape' : 'portrait',
      unit: 'mm',
      format: [pdfWidth, pdfHeight],
    });

    // 添加标题
    if (title) {
      pdf.setFont('helvetica', 'bold');
      pdf.setFontSize(16);
      pdf.setTextColor(0, 0, 0);

      const titleWidth = pdf.getTextWidth(title);
      const titleX = (pdfWidth - titleWidth) / 2;

      pdf.text(title, titleX, margin + 15);

      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.5);
      pdf.line(margin, margin + 25, pdfWidth - margin, margin + 25);
    }

    const yOffset = margin + titleOffset;
    const finalImgWidth = imgWidth * scaleFactor;
    const finalImgHeight = imgHeight * scaleFactor;

    // 添加图片到PDF
    pdf.addImage(imgData, 'JPEG', margin, yOffset, finalImgWidth, finalImgHeight);

    // 保存PDF
    pdf.save(filename);

    console.log('PDF导出成功:', filename);
    return true;
  } catch (error) {
    console.error('PDF导出错误:', error);
    return false;
  }
};
